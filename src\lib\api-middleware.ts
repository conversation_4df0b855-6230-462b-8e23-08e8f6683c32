import { NextRequest, NextResponse } from "next/server"

import { ApiError, handleApiError } from "./api-response"

/**
 * Rate limiting store (in production, use Redis or similar)
 */
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

/**
 * Rate limiting middleware
 */
export function rateLimit(
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
) {
  return (handler: (request: NextRequest) => Promise<NextResponse>) => {
    return async (request: NextRequest): Promise<NextResponse> => {
      const ip = request.ip || request.headers.get("x-forwarded-for") || "unknown"
      const now = Date.now()
      const windowStart = now - windowMs

      // Clean up old entries
      for (const [key, value] of rateLimitStore.entries()) {
        if (value.resetTime < windowStart) {
          rateLimitStore.delete(key)
        }
      }

      // Get current count for this IP
      const current = rateLimitStore.get(ip) || { count: 0, resetTime: now + windowMs }

      if (current.count >= maxRequests && current.resetTime > now) {
        return NextResponse.json(
          {
            message: "Too many requests",
            success: false,
            error: "Rate limit exceeded",
          },
          { status: 429 }
        )
      }

      // Update count
      rateLimitStore.set(ip, {
        count: current.count + 1,
        resetTime: current.resetTime,
      })

      return handler(request)
    }
  }
}

/**
 * CORS middleware
 */
export function cors(
  origins: string[] = ["*"],
  methods: string[] = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
) {
  return (handler: (request: NextRequest) => Promise<NextResponse>) => {
    return async (request: NextRequest): Promise<NextResponse> => {
      const origin = request.headers.get("origin")
      const isAllowed = origins.includes("*") || (origin && origins.includes(origin))

      // Handle preflight requests
      if (request.method === "OPTIONS") {
        return new NextResponse(null, {
          status: 200,
          headers: {
            "Access-Control-Allow-Origin": isAllowed ? origin || "*" : "null",
            "Access-Control-Allow-Methods": methods.join(", "),
            "Access-Control-Allow-Headers": "Content-Type, Authorization",
            "Access-Control-Max-Age": "86400",
          },
        })
      }

      const response = await handler(request)

      // Add CORS headers to response
      if (isAllowed) {
        response.headers.set("Access-Control-Allow-Origin", origin || "*")
        response.headers.set("Access-Control-Allow-Methods", methods.join(", "))
        response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization")
      }

      return response
    }
  }
}

/**
 * Authentication middleware
 */
export function requireAuth() {
  return (handler: (request: NextRequest) => Promise<NextResponse>) => {
    return async (request: NextRequest): Promise<NextResponse> => {
      const authHeader = request.headers.get("authorization")
      
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        throw new ApiError("Authentication required", 401, "UNAUTHORIZED")
      }

      const token = authHeader.substring(7)
      
      // TODO: Validate token (implement your auth logic here)
      if (!token || token === "invalid") {
        throw new ApiError("Invalid token", 401, "INVALID_TOKEN")
      }

      // Add user info to request (you can extend NextRequest type)
      // request.user = await validateToken(token)

      return handler(request)
    }
  }
}

/**
 * Logging middleware
 */
export function logging() {
  return (handler: (request: NextRequest) => Promise<NextResponse>) => {
    return async (request: NextRequest): Promise<NextResponse> => {
      const start = Date.now()
      const method = request.method
      const url = request.url

      console.log(`🚀 ${method} ${url}`)

      try {
        const response = await handler(request)
        const duration = Date.now() - start
        console.log(`✅ ${method} ${url} - ${response.status} (${duration}ms)`)
        return response
      } catch (error) {
        const duration = Date.now() - start
        console.error(`❌ ${method} ${url} - Error (${duration}ms):`, error)
        return handleApiError(error)
      }
    }
  }
}

/**
 * Validation middleware
 */
export function validate<T>(schema: {
  parse: (data: unknown) => T
}) {
  return (handler: (request: NextRequest, data: T) => Promise<NextResponse>) => {
    return async (request: NextRequest): Promise<NextResponse> => {
      try {
        const body = await request.json()
        const validatedData = schema.parse(body)
        return handler(request, validatedData)
      } catch (error) {
        if (error instanceof Error && "issues" in error) {
          // Zod validation error
          throw new ApiError("Validation failed", 400, "VALIDATION_ERROR")
        }
        throw new ApiError("Invalid request body", 400, "INVALID_BODY")
      }
    }
  }
}

/**
 * Compose multiple middlewares
 */
export function compose(...middlewares: Array<(handler: any) => any>) {
  return (handler: any) => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), handler)
  }
}

/**
 * Error handling wrapper
 */
export function withErrorHandling(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      return await handler(request)
    } catch (error) {
      return handleApiError(error)
    }
  }
}
