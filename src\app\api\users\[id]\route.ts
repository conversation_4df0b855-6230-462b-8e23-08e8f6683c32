import { NextRequest } from "next/server"
import { z } from "zod"

import {
  ApiError,
  createSuccessResponse,
  validateMethod,
} from "@/lib/api-response"
import { compose, logging, rateLimit, withErrorHandling } from "@/lib/api-middleware"

// Mock data (should match the one in users/route.ts)
const mockUsers = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>", createdAt: new Date() },
  { id: "2", name: "<PERSON>", email: "<EMAIL>", createdAt: new Date() },
  { id: "3", name: "<PERSON>", email: "<EMAIL>", createdAt: new Date() },
]

// Validation schemas
const updateUserSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  email: z.string().email("Invalid email address").optional(),
})

/**
 * GET /api/users/[id] - Get a specific user
 */
async function getUser(request: NextRequest, { params }: { params: { id: string } }) {
  validateMethod(request, ["GET"])

  const user = mockUsers.find((u) => u.id === params.id)
  if (!user) {
    throw new ApiError("User not found", 404, "USER_NOT_FOUND")
  }

  return createSuccessResponse(user, "User retrieved successfully")
}

/**
 * PATCH /api/users/[id] - Update a specific user
 */
async function updateUser(request: NextRequest, { params }: { params: { id: string } }) {
  validateMethod(request, ["PATCH"])

  const userIndex = mockUsers.findIndex((u) => u.id === params.id)
  if (userIndex === -1) {
    throw new ApiError("User not found", 404, "USER_NOT_FOUND")
  }

  const body = await request.json()
  const validatedData = updateUserSchema.parse(body)

  // Check if email is being updated and already exists
  if (validatedData.email) {
    const existingUser = mockUsers.find(
      (user) => user.email === validatedData.email && user.id !== params.id
    )
    if (existingUser) {
      throw new ApiError("User with this email already exists", 409, "EMAIL_EXISTS")
    }
  }

  // Update user
  mockUsers[userIndex] = {
    ...mockUsers[userIndex],
    ...validatedData,
  }

  return createSuccessResponse(mockUsers[userIndex], "User updated successfully")
}

/**
 * DELETE /api/users/[id] - Delete a specific user
 */
async function deleteUser(request: NextRequest, { params }: { params: { id: string } }) {
  validateMethod(request, ["DELETE"])

  const userIndex = mockUsers.findIndex((u) => u.id === params.id)
  if (userIndex === -1) {
    throw new ApiError("User not found", 404, "USER_NOT_FOUND")
  }

  // Remove user
  const deletedUser = mockUsers.splice(userIndex, 1)[0]

  return createSuccessResponse(deletedUser, "User deleted successfully")
}

/**
 * Main handler with middleware
 */
const handler = compose(
  rateLimit(100, 15 * 60 * 1000),
  logging(),
  withErrorHandling
)(async (request: NextRequest, context: { params: { id: string } }) => {
  switch (request.method) {
    case "GET":
      return getUser(request, context)
    case "PATCH":
      return updateUser(request, context)
    case "DELETE":
      return deleteUser(request, context)
    default:
      validateMethod(request, ["GET", "PATCH", "DELETE"])
  }
})

export { handler as GET, handler as PATCH, handler as DELETE }
