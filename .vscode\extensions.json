{
  "recommendations": [
    // Essential extensions
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    
    // React and Next.js
    "ms-vscode.vscode-react-javascript",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    
    // Git and version control
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    
    // Testing
    "orta.vscode-jest",
    "ms-vscode.test-adapter-converter",
    
    // Code quality and productivity
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "christian-kohler.npm-intellisense",
    "ms-vscode.vscode-json",
    
    // Themes and icons
    "pkief.material-icon-theme",
    "zhuangtongfa.material-theme",
    
    // Markdown
    "yzhang.markdown-all-in-one",
    "davidanson.vscode-markdownlint",
    
    // Utilities
    "ms-vscode.vscode-typescript-next",
    "gruntfuggly.todo-tree",
    "alefragnani.bookmarks",
    "ms-vscode.vscode-json",
    
    // API development
    "humao.rest-client",
    "rangav.vscode-thunder-client"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify",
    "ms-vscode.vscode-css-peek"
  ]
}
