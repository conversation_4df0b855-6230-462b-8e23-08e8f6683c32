# Next.js Production-Ready Starter

A comprehensive Next.js 15 starter template with TypeScript, shadcn/ui, React Query, and all the modern tools you need for production-grade applications.

## 🚀 Features

- **Next.js 15** with App Router and React Server Components
- **TypeScript** for type safety and better developer experience
- **shadcn/ui** component library with Tailwind CSS
- **React Query (TanStack Query)** for state management and data fetching
- **Axios** HTTP client with interceptors and error handling
- **ESLint & Prettier** for code quality and formatting
- **Jest & React Testing Library** for comprehensive testing
- **Dark/Light mode** toggle with next-themes
- **Environment configuration** with validation using Zod
- **API routes** with proper error handling and middleware
- **Production-ready** folder structure and configurations

## 📦 What's Included

### Core Technologies
- Next.js 15 with TypeScript and App Router
- shadcn/ui component library with Tailwind CSS
- ESLint and Prettier configuration
- React Query for state management
- Axios HTTP client with interceptors
- Environment configuration with validation
- Dark/light mode toggle with next-themes
- API routes with proper error handling
- Production-ready folder structure

### Development Tools
- Jest and React Testing Library setup
- TypeScript configuration with absolute imports
- ESLint with Next.js and TypeScript rules
- Prettier with Tailwind CSS plugin
- VS Code settings and recommended extensions

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- pnpm (recommended) or npm/yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd next-qop-test
```

2. Install dependencies:
```bash
pnpm install
```

3. Copy environment variables:
```bash
cp .env.example .env.local
```

4. Start the development server:
```bash
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── components/        # Components showcase page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── providers/        # Context providers
│   └── ui/               # shadcn/ui components
├── hooks/                 # Custom React hooks
├── lib/                   # Utility libraries
├── types/                 # TypeScript type definitions
├── utils/                 # Utility functions
├── config/               # Configuration files
└── __tests__/            # Test files
```

## 🧪 Testing

Run tests with:

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run tests for CI
pnpm test:ci
```

## 🎨 Styling

This project uses Tailwind CSS with shadcn/ui components. The design system includes:

- **Color scheme**: Customizable with CSS variables
- **Dark mode**: Automatic system detection with manual toggle
- **Components**: Pre-built accessible components from shadcn/ui
- **Typography**: Responsive typography scale
- **Spacing**: Consistent spacing system

### Adding New Components

To add new shadcn/ui components:

```bash
pnpm dlx shadcn@latest add [component-name]
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Authentication
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000

# Feature flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE=false
```

### TypeScript

The project includes comprehensive TypeScript configuration with:
- Strict mode enabled
- Absolute imports configured
- Path mapping for clean imports

### ESLint & Prettier

Code quality tools are configured with:
- Next.js recommended rules
- TypeScript support
- Prettier integration
- Import sorting
- Tailwind CSS class sorting

## 📚 API Routes

The project includes example API routes with:

- **Error handling**: Consistent error responses
- **Validation**: Request validation with Zod
- **Middleware**: Rate limiting, CORS, logging
- **Type safety**: Fully typed request/response

Example endpoints:
- `GET /api/health` - Health check
- `GET /api/users` - Get users with pagination
- `POST /api/users` - Create user
- `GET /api/users/[id]` - Get user by ID

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables
4. Deploy!

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework
- [shadcn/ui](https://ui.shadcn.com/) - Beautiful component library
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [React Query](https://tanstack.com/query) - Data fetching library
- [Vercel](https://vercel.com/) - Deployment platform
