{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\"\n\n/**\n * Global middleware for the application\n * Runs on all requests before they reach the API routes or pages\n */\nexport function middleware(request: NextRequest) {\n  // Add security headers\n  const response = NextResponse.next()\n\n  // Security headers\n  response.headers.set(\"X-Frame-Options\", \"DENY\")\n  response.headers.set(\"X-Content-Type-Options\", \"nosniff\")\n  response.headers.set(\"Referrer-Policy\", \"strict-origin-when-cross-origin\")\n  response.headers.set(\n    \"Permissions-Policy\",\n    \"camera=(), microphone=(), geolocation=()\"\n  )\n\n  // API-specific middleware\n  if (request.nextUrl.pathname.startsWith(\"/api/\")) {\n    // Add API-specific headers\n    response.headers.set(\"X-API-Version\", \"1.0.0\")\n    \n    // Log API requests in development\n    if (process.env.NODE_ENV === \"development\") {\n      console.log(`API Request: ${request.method} ${request.nextUrl.pathname}`)\n    }\n  }\n\n  return response\n}\n\n/**\n * Configure which paths the middleware should run on\n */\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    \"/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)\",\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAMO,SAAS,WAAW,OAAoB;IAC7C,uBAAuB;IACvB,MAAM,WAAW,8VAAA,CAAA,eAAY,CAAC,IAAI;IAElC,mBAAmB;IACnB,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IACxC,SAAS,OAAO,CAAC,GAAG,CAClB,sBACA;IAGF,0BAA0B;IAC1B,IAAI,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU;QAChD,2BAA2B;QAC3B,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAEtC,kCAAkC;QAClC,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE;QAC1E;IACF;IAEA,OAAO;AACT;AAKO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}