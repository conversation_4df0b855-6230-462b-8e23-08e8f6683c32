import { AxiosResponse } from "axios"

import { api, ApiError, ApiResponse } from "./http-client"

/**
 * Generic function to handle API responses
 */
export async function handleApiResponse<T>(
  apiCall: Promise<AxiosResponse<ApiResponse<T>>>
): Promise<T> {
  try {
    const response = await apiCall
    return response.data.data
  } catch (error) {
    // Re-throw the error so it can be handled by React Query or the calling code
    throw error as ApiError
  }
}

/**
 * Upload file utility
 */
export async function uploadFile(
  file: File,
  endpoint: string = "/upload",
  onProgress?: (progress: number) => void
): Promise<{ url: string; filename: string }> {
  const formData = new FormData()
  formData.append("file", file)

  try {
    const response = await api.post<{ url: string; filename: string }>(
      endpoint,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            )
            onProgress(progress)
          }
        },
      }
    )

    return response.data.data
  } catch (error) {
    throw error as ApiError
  }
}

/**
 * Download file utility
 */
export async function downloadFile(
  url: string,
  filename?: string
): Promise<void> {
  try {
    const response = await api.get(url, {
      responseType: "blob",
    })

    // Create blob link to download
    const blob = new Blob([response.data])
    const link = document.createElement("a")
    link.href = window.URL.createObjectURL(blob)
    link.download = filename || "download"
    
    // Trigger download
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // Clean up
    window.URL.revokeObjectURL(link.href)
  } catch (error) {
    throw error as ApiError
  }
}

/**
 * Paginated API call utility
 */
export interface PaginationParams {
  page?: number
  limit?: number
  sort?: string
  order?: "asc" | "desc"
  search?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export async function fetchPaginated<T>(
  endpoint: string,
  params: PaginationParams = {}
): Promise<PaginatedResponse<T>> {
  const {
    page = 1,
    limit = 10,
    sort,
    order = "desc",
    search,
  } = params

  const queryParams = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    order,
  })

  if (sort) queryParams.append("sort", sort)
  if (search) queryParams.append("search", search)

  return handleApiResponse(
    api.get<PaginatedResponse<T>>(`${endpoint}?${queryParams.toString()}`)
  )
}

/**
 * Retry utility for failed requests
 */
export async function retryRequest<T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error as Error
      
      if (i === maxRetries) {
        break
      }

      // Wait before retrying
      await new Promise((resolve) => setTimeout(resolve, delay * (i + 1)))
    }
  }

  throw lastError!
}

/**
 * Batch requests utility
 */
export async function batchRequests<T>(
  requests: Array<() => Promise<T>>,
  batchSize: number = 5
): Promise<T[]> {
  const results: T[] = []
  
  for (let i = 0; i < requests.length; i += batchSize) {
    const batch = requests.slice(i, i + batchSize)
    const batchResults = await Promise.all(batch.map((request) => request()))
    results.push(...batchResults)
  }
  
  return results
}
