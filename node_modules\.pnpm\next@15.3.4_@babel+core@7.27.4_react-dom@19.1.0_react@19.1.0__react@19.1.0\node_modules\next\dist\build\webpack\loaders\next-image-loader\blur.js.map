{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-image-loader/blur.ts"], "sourcesContent": ["import isAnimated from 'next/dist/compiled/is-animated'\nimport { optimizeImage } from '../../../../server/image-optimizer'\n\nconst BLUR_IMG_SIZE = 8\nconst BLUR_QUALITY = 70\nconst VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match other usages\n\nexport async function getBlurImage(\n  content: Buffer,\n  extension: string,\n  imageSize: { width: number; height: number },\n  {\n    basePath,\n    outputPath,\n    isDev,\n    tracing = () => ({\n      traceFn:\n        (fn) =>\n        (...args: any) =>\n          fn(...args),\n      traceAsyncFn:\n        (fn) =>\n        (...args: any) =>\n          fn(...args),\n    }),\n  }: {\n    basePath: string\n    outputPath: string\n    isDev: boolean\n    tracing: (name?: string) => {\n      traceFn(fn: Function): any\n      traceAsyncFn(fn: Function): any\n    }\n  }\n) {\n  let blurDataURL: string | undefined\n  let blurWidth: number = 0\n  let blurHeight: number = 0\n\n  if (VALID_BLUR_EXT.includes(extension) && !isAnimated(content)) {\n    // Shrink the image's largest dimension\n    if (imageSize.width >= imageSize.height) {\n      blurWidth = BLUR_IMG_SIZE\n      blurHeight = Math.max(\n        Math.round((imageSize.height / imageSize.width) * BLUR_IMG_SIZE),\n        1\n      )\n    } else {\n      blurWidth = Math.max(\n        Math.round((imageSize.width / imageSize.height) * BLUR_IMG_SIZE),\n        1\n      )\n      blurHeight = BLUR_IMG_SIZE\n    }\n\n    if (isDev) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, we inline a\n      // special url to lazily generate the blur placeholder at request time.\n      const prefix = 'http://localhost'\n      const url = new URL(`${basePath || ''}/_next/image`, prefix)\n      url.searchParams.set('url', outputPath)\n      url.searchParams.set('w', String(blurWidth))\n      url.searchParams.set('q', String(BLUR_QUALITY))\n      blurDataURL = url.href.slice(prefix.length)\n    } else {\n      const resizeImageSpan = tracing('image-resize')\n      const resizedImage = await resizeImageSpan.traceAsyncFn(() =>\n        optimizeImage({\n          buffer: content,\n          width: blurWidth,\n          height: blurHeight,\n          contentType: `image/${extension}`,\n          quality: BLUR_QUALITY,\n        })\n      )\n      const blurDataURLSpan = tracing('image-base64-tostring')\n      blurDataURL = blurDataURLSpan.traceFn(\n        () =>\n          `data:image/${extension};base64,${resizedImage.toString('base64')}`\n      )\n    }\n  }\n  return {\n    dataURL: blurDataURL,\n    width: blurWidth,\n    height: blurHeight,\n  }\n}\n"], "names": ["getBlurImage", "BLUR_IMG_SIZE", "BLUR_QUALITY", "VALID_BLUR_EXT", "content", "extension", "imageSize", "basePath", "outputPath", "isDev", "tracing", "traceFn", "fn", "args", "traceAsyncFn", "blurDataURL", "blur<PERSON>idth", "blurHeight", "includes", "isAnimated", "width", "height", "Math", "max", "round", "prefix", "url", "URL", "searchParams", "set", "String", "href", "slice", "length", "resizeImageSpan", "resizedImage", "optimizeImage", "buffer", "contentType", "quality", "blurDataURLSpan", "toString", "dataURL"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;mEAPC;gCACO;;;;;;AAE9B,MAAMC,gBAAgB;AACtB,MAAMC,eAAe;AACrB,MAAMC,iBAAiB;IAAC;IAAQ;IAAO;IAAQ;CAAO,CAAC,4BAA4B;;AAE5E,eAAeH,aACpBI,OAAe,EACfC,SAAiB,EACjBC,SAA4C,EAC5C,EACEC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,UAAU,IAAO,CAAA;QACfC,SACE,CAACC,KACD,CAAC,GAAGC,OACFD,MAAMC;QACVC,cACE,CAACF,KACD,CAAC,GAAGC,OACFD,MAAMC;IACZ,CAAA,CAAE,EASH;IAED,IAAIE;IACJ,IAAIC,YAAoB;IACxB,IAAIC,aAAqB;IAEzB,IAAId,eAAee,QAAQ,CAACb,cAAc,CAACc,IAAAA,mBAAU,EAACf,UAAU;QAC9D,uCAAuC;QACvC,IAAIE,UAAUc,KAAK,IAAId,UAAUe,MAAM,EAAE;YACvCL,YAAYf;YACZgB,aAAaK,KAAKC,GAAG,CACnBD,KAAKE,KAAK,CAAC,AAAClB,UAAUe,MAAM,GAAGf,UAAUc,KAAK,GAAInB,gBAClD;QAEJ,OAAO;YACLe,YAAYM,KAAKC,GAAG,CAClBD,KAAKE,KAAK,CAAC,AAAClB,UAAUc,KAAK,GAAGd,UAAUe,MAAM,GAAIpB,gBAClD;YAEFgB,aAAahB;QACf;QAEA,IAAIQ,OAAO;YACT,8EAA8E;YAC9E,qEAAqE;YACrE,uEAAuE;YACvE,MAAMgB,SAAS;YACf,MAAMC,MAAM,IAAIC,IAAI,GAAGpB,YAAY,GAAG,YAAY,CAAC,EAAEkB;YACrDC,IAAIE,YAAY,CAACC,GAAG,CAAC,OAAOrB;YAC5BkB,IAAIE,YAAY,CAACC,GAAG,CAAC,KAAKC,OAAOd;YACjCU,IAAIE,YAAY,CAACC,GAAG,CAAC,KAAKC,OAAO5B;YACjCa,cAAcW,IAAIK,IAAI,CAACC,KAAK,CAACP,OAAOQ,MAAM;QAC5C,OAAO;YACL,MAAMC,kBAAkBxB,QAAQ;YAChC,MAAMyB,eAAe,MAAMD,gBAAgBpB,YAAY,CAAC,IACtDsB,IAAAA,6BAAa,EAAC;oBACZC,QAAQjC;oBACRgB,OAAOJ;oBACPK,QAAQJ;oBACRqB,aAAa,CAAC,MAAM,EAAEjC,WAAW;oBACjCkC,SAASrC;gBACX;YAEF,MAAMsC,kBAAkB9B,QAAQ;YAChCK,cAAcyB,gBAAgB7B,OAAO,CACnC,IACE,CAAC,WAAW,EAAEN,UAAU,QAAQ,EAAE8B,aAAaM,QAAQ,CAAC,WAAW;QAEzE;IACF;IACA,OAAO;QACLC,SAAS3B;QACTK,OAAOJ;QACPK,QAAQJ;IACV;AACF"}