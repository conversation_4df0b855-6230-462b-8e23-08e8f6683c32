/* eslint-disable import/no-extraneous-dependencies */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "createProxy", {
    enumerable: true,
    get: function() {
        return createProxy;
    }
});
const _serveredge = require("react-server-dom-webpack/server.edge");
const createProxy = _serveredge.createClientModuleProxy;

//# sourceMappingURL=module-proxy.js.map