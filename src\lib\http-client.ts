import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios"

import { appConfig } from "@/config/environment"

// Types for our HTTP client
export interface ApiError {
  message: string
  code?: string
  status?: number
  details?: unknown
}

export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  success: boolean
}

/**
 * Create an Axios instance with default configuration
 */
function createHttpClient(): AxiosInstance {
  const client = axios.create({
    baseURL: appConfig.apiUrl,
    timeout: 10000, // 10 seconds
    headers: {
      "Content-Type": "application/json",
    },
  })

  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add auth token if available
      if (typeof window !== "undefined") {
        const token = localStorage.getItem("auth_token")
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
      }

      // Add request timestamp for debugging
      config.metadata = { startTime: new Date() }

      // Log request in development
      if (process.env.NODE_ENV === "development") {
        console.log(`🚀 ${config.method?.toUpperCase()} ${config.url}`, {
          data: config.data,
          params: config.params,
        })
      }

      return config
    },
    (error) => {
      console.error("Request interceptor error:", error)
      return Promise.reject(error)
    }
  )

  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Calculate request duration
      const duration = response.config.metadata?.startTime
        ? new Date().getTime() - response.config.metadata.startTime.getTime()
        : 0

      // Log response in development
      if (process.env.NODE_ENV === "development") {
        console.log(
          `✅ ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`,
          response.data
        )
      }

      return response
    },
    (error: AxiosError) => {
      // Calculate request duration
      const duration = error.config?.metadata?.startTime
        ? new Date().getTime() - error.config.metadata.startTime.getTime()
        : 0

      // Log error in development
      if (process.env.NODE_ENV === "development") {
        console.error(
          `❌ ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`,
          error.response?.data || error.message
        )
      }

      // Handle different error scenarios
      if (error.response) {
        // Server responded with error status
        const apiError: ApiError = {
          message:
            error.response.data?.message ||
            error.message ||
            "An error occurred",
          code: error.response.data?.code || error.code,
          status: error.response.status,
          details: error.response.data,
        }

        // Handle specific status codes
        switch (error.response.status) {
          case 401:
            // Unauthorized - clear auth token and redirect to login
            if (typeof window !== "undefined") {
              localStorage.removeItem("auth_token")
              // You might want to redirect to login page here
              // window.location.href = '/login'
            }
            break
          case 403:
            // Forbidden
            apiError.message = "You don't have permission to perform this action"
            break
          case 404:
            // Not found
            apiError.message = "The requested resource was not found"
            break
          case 422:
            // Validation error
            apiError.message = "Validation failed"
            break
          case 429:
            // Rate limit exceeded
            apiError.message = "Too many requests. Please try again later."
            break
          case 500:
            // Server error
            apiError.message = "Internal server error. Please try again later."
            break
        }

        return Promise.reject(apiError)
      } else if (error.request) {
        // Network error
        const networkError: ApiError = {
          message: "Network error. Please check your connection.",
          code: "NETWORK_ERROR",
          status: 0,
        }
        return Promise.reject(networkError)
      } else {
        // Something else happened
        const unknownError: ApiError = {
          message: error.message || "An unexpected error occurred",
          code: "UNKNOWN_ERROR",
        }
        return Promise.reject(unknownError)
      }
    }
  )

  return client
}

// Create the HTTP client instance
export const httpClient = createHttpClient()

// Convenience methods
export const api = {
  get: <T = unknown>(url: string, config?: AxiosRequestConfig) =>
    httpClient.get<ApiResponse<T>>(url, config),

  post: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
    httpClient.post<ApiResponse<T>>(url, data, config),

  put: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
    httpClient.put<ApiResponse<T>>(url, data, config),

  patch: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
    httpClient.patch<ApiResponse<T>>(url, data, config),

  delete: <T = unknown>(url: string, config?: AxiosRequestConfig) =>
    httpClient.delete<ApiResponse<T>>(url, config),
}

// Extend AxiosRequestConfig to include metadata
declare module "axios" {
  interface AxiosRequestConfig {
    metadata?: {
      startTime: Date
    }
  }
}
