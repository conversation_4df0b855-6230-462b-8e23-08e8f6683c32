/**
 * Environment configuration with validation
 * This ensures type safety for environment variables
 */

import { z } from "zod"

// Client-side environment variables (prefixed with NEXT_PUBLIC_)
const clientEnvSchema = z.object({
  NEXT_PUBLIC_APP_URL: z.string().url().default("http://localhost:3000"),
  NEXT_PUBLIC_API_URL: z.string().url().default("http://localhost:3000/api"),
  NEXT_PUBLIC_ENABLE_ANALYTICS: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
  NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE: z
    .string()
    .transform((val) => val === "true")
    .default("false"),
})

// Server-side environment variables
const serverEnvSchema = z.object({
  NODE_ENV: z.enum(["development", "staging", "production"]).default("development"),
  DATABASE_URL: z.string().optional(),
  NEXTAUTH_SECRET: z.string().min(1, "NEXTAUTH_SECRET is required"),
  NEXTAUTH_URL: z.string().url().optional(),
  
  // OAuth providers
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  GITHUB_CLIENT_ID: z.string().optional(),
  GITHUB_CLIENT_SECRET: z.string().optional(),
  
  // Email configuration
  EMAIL_SERVER_HOST: z.string().optional(),
  EMAIL_SERVER_PORT: z.string().transform(Number).optional(),
  EMAIL_SERVER_USER: z.string().optional(),
  EMAIL_SERVER_PASSWORD: z.string().optional(),
  EMAIL_FROM: z.string().email().optional(),
  
  // File upload
  UPLOAD_MAX_SIZE: z.string().transform(Number).default("10485760"), // 10MB
  UPLOAD_ALLOWED_TYPES: z.string().default("image/jpeg,image/png,image/gif,application/pdf"),
  
  // External APIs
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  
  // Analytics
  GOOGLE_ANALYTICS_ID: z.string().optional(),
  
  // Rate limiting
  RATE_LIMIT_MAX: z.string().transform(Number).default("100"),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default("900000"), // 15 minutes
  
  // Logging
  LOG_LEVEL: z.enum(["error", "warn", "info", "debug"]).default("info"),
})

// Combined schema for validation
const envSchema = clientEnvSchema.merge(serverEnvSchema)

export type Env = z.infer<typeof envSchema>
export type ClientEnv = z.infer<typeof clientEnvSchema>
export type ServerEnv = z.infer<typeof serverEnvSchema>

// Validate environment variables
function validateEnv(): Env {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("❌ Invalid environment variables:", error)
    throw new Error("Invalid environment variables")
  }
}

// Validate only client environment variables (safe for client-side)
function validateClientEnv(): ClientEnv {
  const clientEnv = Object.keys(clientEnvSchema.shape).reduce(
    (acc, key) => {
      acc[key] = process.env[key]
      return acc
    },
    {} as Record<string, string | undefined>
  )
  
  try {
    return clientEnvSchema.parse(clientEnv)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("❌ Invalid client environment variables:", error)
    throw new Error("Invalid client environment variables")
  }
}

// Export validated environment variables
export const env = validateEnv()
export const clientEnv = validateClientEnv()

// App configuration
export const appConfig = {
  name: "Next.js App",
  description: "A modern Next.js application",
  version: "1.0.0",
  url: env.NEXT_PUBLIC_APP_URL,
  apiUrl: env.NEXT_PUBLIC_API_URL,
  environment: env.NODE_ENV,
  features: {
    analytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    maintenanceMode: env.NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE,
  },
  upload: {
    maxSize: env.UPLOAD_MAX_SIZE,
    allowedTypes: env.UPLOAD_ALLOWED_TYPES.split(","),
  },
  rateLimit: {
    max: env.RATE_LIMIT_MAX,
    window: env.RATE_LIMIT_WINDOW,
  },
} as const

// Runtime environment checks
export const isProduction = env.NODE_ENV === "production"
export const isDevelopment = env.NODE_ENV === "development"
export const isStaging = env.NODE_ENV === "staging"

// Feature flags
export const features = {
  analytics: env.NEXT_PUBLIC_ENABLE_ANALYTICS,
  maintenanceMode: env.NEXT_PUBLIC_ENABLE_MAINTENANCE_MODE,
  oauth: {
    google: !!(env.GOOGLE_CLIENT_ID && env.GOOGLE_CLIENT_SECRET),
    github: !!(env.GITHUB_CLIENT_ID && env.GITHUB_CLIENT_SECRET),
  },
  email: !!(env.EMAIL_SERVER_HOST && env.EMAIL_SERVER_USER),
  stripe: !!(env.STRIPE_SECRET_KEY && env.STRIPE_PUBLISHABLE_KEY),
} as const
