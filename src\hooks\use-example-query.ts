import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

import { handleApiResponse } from "@/lib/api-utils"
import { api } from "@/lib/http-client"
import { queryKeys } from "@/lib/react-query"

// Example types
interface User {
  id: string
  name: string
  email: string
}

interface CreateUserData {
  name: string
  email: string
}

// Example API functions using our HTTP client
const fetchUsers = async (): Promise<User[]> => {
  return handleApiResponse(api.get<User[]>("/users"))
}

const fetchUser = async (id: string): Promise<User> => {
  return handleApiResponse(api.get<User>(`/users/${id}`))
}

const createUser = async (userData: CreateUserData): Promise<User> => {
  return handleApiResponse(api.post<User>("/users", userData))
}

// Custom hooks
export function useUsers() {
  return useQuery({
    queryKey: queryKeys.users(),
    queryFn: fetchUsers,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useUser(id: string) {
  return useQuery({
    queryKey: queryKeys.user(id),
    queryFn: () => fetchUser(id),
    enabled: !!id, // Only run query if id is provided
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: createUser,
    onSuccess: (newUser) => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: queryKeys.users() })
      
      // Optionally, add the new user to the cache
      queryClient.setQueryData(queryKeys.user(newUser.id), newUser)
    },
    onError: (error) => {
      // Handle error (you might want to show a toast notification)
      console.error("Failed to create user:", error)
    },
  })
}

// Example of optimistic updates
export function useUpdateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<User> }) => {
      return handleApiResponse(api.patch<User>(`/users/${id}`, data))
    },
    onMutate: async ({ id, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.user(id) })

      // Snapshot the previous value
      const previousUser = queryClient.getQueryData(queryKeys.user(id))

      // Optimistically update to the new value
      queryClient.setQueryData(queryKeys.user(id), (old: User | undefined) => ({
        ...old!,
        ...data,
      }))

      // Return a context object with the snapshotted value
      return { previousUser }
    },
    onError: (_error, { id }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      queryClient.setQueryData(queryKeys.user(id), context?.previousUser)
    },
    onSettled: (_data, _error, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.user(id) })
    },
  })
}
