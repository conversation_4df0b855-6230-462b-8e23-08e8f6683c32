/**
 * Environment configuration with validation
 * This ensures type safety for environment variables
 */

import { z } from "zod"

const envSchema = z.object({
  NODE_ENV: z
    .enum(["development", "staging", "production"])
    .default("development"),
  NEXT_PUBLIC_APP_URL: z.string().url().optional(),
  NEXT_PUBLIC_API_URL: z.string().url().optional(),
  DATABASE_URL: z.string().optional(),
  NEXTAUTH_SECRET: z.string().optional(),
  NEXTAUTH_URL: z.string().url().optional(),
})

export type Env = z.infer<typeof envSchema>

// Validate environment variables
function validateEnv(): Env {
  try {
    return envSchema.parse(process.env)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("❌ Invalid environment variables:", error)
    throw new Error("Invalid environment variables")
  }
}

export const env = validateEnv()

// App configuration
export const appConfig = {
  name: "Next.js App",
  description: "A modern Next.js application",
  url: env.NEXT_PUBLIC_APP_URL || "http://localhost:3000",
  apiUrl: env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api",
  environment: env.NODE_ENV,
} as const
