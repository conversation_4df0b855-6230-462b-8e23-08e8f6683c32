import { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { ThemeProvider } from '@/components/providers/theme-provider'

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem={false}
        disableTransitionOnChange
      >
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Test utilities
export const createMockQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  })
}

// Mock data generators
export const mockUser = {
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  createdAt: new Date('2023-01-01'),
}

export const mockUsers = [
  mockUser,
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    createdAt: new Date('2023-01-02'),
  },
]

// API response mocks
export const mockApiResponse = <T>(data: T) => ({
  data,
  success: true,
  message: 'Success',
})

export const mockApiError = (message: string, status: number = 500) => ({
  success: false,
  error: message,
  message,
  status,
})

// Event helpers
export const createMockEvent = (overrides = {}) => ({
  preventDefault: jest.fn(),
  stopPropagation: jest.fn(),
  target: { value: '' },
  ...overrides,
})

// Async test helpers
export const waitForLoadingToFinish = () =>
  new Promise((resolve) => setTimeout(resolve, 0))

// Component test helpers
export const getByTestId = (container: HTMLElement, testId: string) =>
  container.querySelector(`[data-testid="${testId}"]`)

export const getAllByTestId = (container: HTMLElement, testId: string) =>
  container.querySelectorAll(`[data-testid="${testId}"]`)

// Form test helpers
export const fillForm = async (
  getByLabelText: (text: string) => HTMLElement,
  userEvent: any,
  formData: Record<string, string>
) => {
  for (const [label, value] of Object.entries(formData)) {
    const input = getByLabelText(label)
    await userEvent.clear(input)
    await userEvent.type(input, value)
  }
}

// Local storage test helpers
export const mockLocalStorage = () => {
  const store: Record<string, string> = {}
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key]
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key])
    }),
  }
}
