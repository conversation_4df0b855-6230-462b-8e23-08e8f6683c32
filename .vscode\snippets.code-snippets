{"React Functional Component": {"prefix": "rfc", "body": ["interface ${1:ComponentName}Props {", "  ${2:// props}", "}", "", "export function ${1:ComponentName}({ ${3:props} }: ${1:ComponentName}Props) {", "  return (", "    <div>", "      ${4:// component content}", "    </div>", "  )", "}"], "description": "Create a React functional component with TypeScript"}, "React Client Component": {"prefix": "rcc", "body": ["\"use client\"", "", "interface ${1:ComponentName}Props {", "  ${2:// props}", "}", "", "export function ${1:ComponentName}({ ${3:props} }: ${1:ComponentName}Props) {", "  return (", "    <div>", "      ${4:// component content}", "    </div>", "  )", "}"], "description": "Create a React client component with TypeScript"}, "Next.js Page Component": {"prefix": "npage", "body": ["import { <PERSON>ada<PERSON> } from 'next'", "", "export const metadata: Metadata = {", "  title: '${1:Page Title}',", "  description: '${2:Page description}',", "}", "", "export default function ${3:PageName}() {", "  return (", "    <div>", "      <h1>${1:Page Title}</h1>", "      ${4:// page content}", "    </div>", "  )", "}"], "description": "Create a Next.js page component with metadata"}, "API Route Handler": {"prefix": "napi", "body": ["import { NextRequest } from 'next/server'", "", "import { createSuccessResponse, validateMethod } from '@/lib/api-response'", "import { withErrorHandling } from '@/lib/api-middleware'", "", "async function handler(request: NextRequest) {", "  validateMethod(request, ['${1:GET}'])", "", "  ${2:// API logic here}", "", "  return createSuccessResponse(${3:data}, '${4:Success message}')", "}", "", "export const ${1:GET} = withErrorHandling(handler)"], "description": "Create a Next.js API route handler"}, "React Query Hook": {"prefix": "rqhook", "body": ["import { useQuery } from '@tanstack/react-query'", "", "import { ${1:apiFunction} } from '@/lib/api'", "import { queryKeys } from '@/lib/react-query'", "", "export function use${2:HookName}(${3:params}) {", "  return useQuery({", "    queryKey: queryKeys.${4:queryKey}(${3:params}),", "    queryFn: () => ${1:apiFunction}(${3:params}),", "    ${5:// additional options}", "  })", "}"], "description": "Create a React Query hook"}, "React Mutation Hook": {"prefix": "rmhook", "body": ["import { useMutation, useQueryClient } from '@tanstack/react-query'", "", "import { ${1:apiFunction} } from '@/lib/api'", "import { queryKeys } from '@/lib/react-query'", "", "export function use${2:MutationName}() {", "  const queryClient = useQueryClient()", "", "  return useMutation({", "    mutationFn: ${1:apiFunction},", "    onSuccess: (${3:data}) => {", "      queryClient.invalidateQueries({ queryKey: queryKeys.${4:queryKey}() })", "    },", "    ${5:// additional options}", "  })", "}"], "description": "Create a React Mutation hook"}, "Jest Test Suite": {"prefix": "jtest", "body": ["import { render, screen } from '@/test-utils'", "", "import { ${1:ComponentName} } from '${2:./component-path}'", "", "describe('${1:ComponentName}', () => {", "  it('${3:should render correctly}', () => {", "    render(<${1:ComponentName} ${4:props} />)", "    ", "    expect(screen.getByText('${5:expected text}')).toBeInTheDocument()", "  })", "", "  ${6:// additional tests}", "})"], "description": "Create a Jest test suite"}, "Zod Schema": {"prefix": "<PERSON><PERSON><PERSON>", "body": ["import { z } from 'zod'", "", "export const ${1:schemaName}Schema = z.object({", "  ${2:field}: z.string().min(1, '${3:Field is required}'),", "  ${4:// additional fields}", "})", "", "export type ${1:schemaName} = z.infer<typeof ${1:schemaName}Schema>"], "description": "Create a Zod validation schema"}, "shadcn/ui Component Import": {"prefix": "shad", "body": ["import { ${1:ComponentName} } from '@/components/ui/${2:component-name}'"], "description": "Import a shadcn/ui component"}, "Console Log": {"prefix": "cl", "body": ["console.log('${1:label}:', ${2:variable})"], "description": "Console log with label"}}