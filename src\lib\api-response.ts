import { NextResponse } from "next/server"

/**
 * Standard API response structure
 */
export interface ApiResponse<T = unknown> {
  data?: T
  message?: string
  success: boolean
  error?: string
  code?: string
}

/**
 * API error class for consistent error handling
 */
export class ApiError extends Error {
  public statusCode: number
  public code?: string

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message)
    this.name = "ApiError"
    this.statusCode = statusCode
    this.code = code
  }
}

/**
 * Create a successful API response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  status: number = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      data,
      message,
      success: true,
    },
    { status }
  )
}

/**
 * Create an error API response
 */
export function createErrorResponse(
  message: string,
  status: number = 500,
  code?: string
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      message,
      success: false,
      error: message,
      code,
    },
    { status }
  )
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: unknown): NextResponse<ApiResponse> {
  console.error("API Error:", error)

  if (error instanceof ApiError) {
    return createErrorResponse(error.message, error.statusCode, error.code)
  }

  if (error instanceof Error) {
    return createErrorResponse(error.message, 500)
  }

  return createErrorResponse("An unexpected error occurred", 500)
}

/**
 * Validate request method
 */
export function validateMethod(
  request: Request,
  allowedMethods: string[]
): void {
  if (!allowedMethods.includes(request.method)) {
    throw new ApiError(
      `Method ${request.method} not allowed`,
      405,
      "METHOD_NOT_ALLOWED"
    )
  }
}

/**
 * Parse and validate JSON body
 */
export async function parseJsonBody<T = unknown>(request: Request): Promise<T> {
  try {
    const body = await request.json()
    return body as T
  } catch (error) {
    throw new ApiError("Invalid JSON body", 400, "INVALID_JSON")
  }
}

/**
 * Get query parameters from URL
 */
export function getQueryParams(request: Request): URLSearchParams {
  const url = new URL(request.url)
  return url.searchParams
}

/**
 * Pagination helper
 */
export interface PaginationParams {
  page: number
  limit: number
  offset: number
}

export function getPaginationParams(
  searchParams: URLSearchParams,
  defaultLimit: number = 10,
  maxLimit: number = 100
): PaginationParams {
  const page = Math.max(1, parseInt(searchParams.get("page") || "1", 10))
  const limit = Math.min(
    maxLimit,
    Math.max(1, parseInt(searchParams.get("limit") || defaultLimit.toString(), 10))
  )
  const offset = (page - 1) * limit

  return { page, limit, offset }
}

/**
 * Create paginated response
 */
export interface PaginatedData<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number
): PaginatedData<T> {
  const totalPages = Math.ceil(total / limit)
  const hasNext = page < totalPages
  const hasPrev = page > 1

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
    },
  }
}
