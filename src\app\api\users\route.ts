import { NextRequest } from "next/server"
import { z } from "zod"

import {
  createSuccessResponse,
  createPaginatedResponse,
  getPaginationParams,
  getQueryParams,
  validateMethod,
} from "@/lib/api-response"
import { compose, logging, rateLimit, withErrorHandling } from "@/lib/api-middleware"

// Mock data (replace with actual database calls)
const mockUsers = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>", createdAt: new Date() },
  { id: "2", name: "<PERSON>", email: "<EMAIL>", createdAt: new Date() },
  { id: "3", name: "<PERSON>", email: "<EMAIL>", createdAt: new Date() },
]

// Validation schemas
const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
})

/**
 * GET /api/users - Get all users with pagination
 */
async function getUsers(request: NextRequest) {
  validateMethod(request, ["GET"])

  const searchParams = getQueryParams(request)
  const { page, limit, offset } = getPaginationParams(searchParams)
  const search = searchParams.get("search")

  // Filter users if search query is provided
  let filteredUsers = mockUsers
  if (search) {
    filteredUsers = mockUsers.filter(
      (user) =>
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
    )
  }

  // Simulate pagination
  const paginatedUsers = filteredUsers.slice(offset, offset + limit)
  const total = filteredUsers.length

  const result = createPaginatedResponse(paginatedUsers, total, page, limit)

  return createSuccessResponse(result, "Users retrieved successfully")
}

/**
 * POST /api/users - Create a new user
 */
async function createUser(request: NextRequest) {
  validateMethod(request, ["POST"])

  const body = await request.json()
  const validatedData = createUserSchema.parse(body)

  // Check if user already exists
  const existingUser = mockUsers.find((user) => user.email === validatedData.email)
  if (existingUser) {
    return createSuccessResponse(null, "User with this email already exists", 409)
  }

  // Create new user
  const newUser = {
    id: (mockUsers.length + 1).toString(),
    ...validatedData,
    createdAt: new Date(),
  }

  mockUsers.push(newUser)

  return createSuccessResponse(newUser, "User created successfully", 201)
}

/**
 * Main handler with middleware
 */
const handler = compose(
  rateLimit(100, 15 * 60 * 1000), // 100 requests per 15 minutes
  logging(),
  withErrorHandling
)(async (request: NextRequest) => {
  switch (request.method) {
    case "GET":
      return getUsers(request)
    case "POST":
      return createUser(request)
    default:
      validateMethod(request, ["GET", "POST"])
  }
})

export { handler as GET, handler as POST }
