"use client"

import { QueryProvider } from "./query-provider"
import { ThemeProvider } from "./theme-provider"

interface ProvidersProps {
  children: React.ReactNode
}

/**
 * Combined providers component
 * Add all your providers here to keep the root layout clean
 */
export function Providers({ children }: ProvidersProps) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <QueryProvider>{children}</QueryProvider>
    </ThemeProvider>
  )
}
