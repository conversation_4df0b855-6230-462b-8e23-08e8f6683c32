/**
 * Common types used throughout the application
 */

export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  success: boolean
  error?: string
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface ApiError {
  message: string
  code?: string
  status?: number
}

export type Theme = "light" | "dark" | "system"

export interface AppConfig {
  apiUrl: string
  appName: string
  version: string
  environment: "development" | "staging" | "production"
}

// Form types
export interface FormState {
  isSubmitting: boolean
  errors: Record<string, string>
  success: boolean
}

// Navigation types
export interface NavItem {
  title: string
  href: string
  icon?: React.ComponentType<{ className?: string }>
  children?: NavItem[]
}

// Component props
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}
