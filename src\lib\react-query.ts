import { QueryClient } from "@tanstack/react-query"

/**
 * Create a new QueryClient instance with optimized defaults
 */
export function createQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Time in milliseconds that unused/inactive cache data remains in memory
        gcTime: 1000 * 60 * 60 * 24, // 24 hours
        // Time in milliseconds after data is considered stale
        staleTime: 1000 * 60 * 5, // 5 minutes
        // Retry failed requests
        retry: (failureCount, error) => {
          // Don't retry on 4xx errors (client errors)
          if (error instanceof Error && "status" in error) {
            const status = (error as { status: number }).status
            if (status >= 400 && status < 500) {
              return false
            }
          }
          // Retry up to 3 times for other errors
          return failureCount < 3
        },
        // Retry delay with exponential backoff
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
        // Refetch on window focus in production
        refetchOnWindowFocus: process.env.NODE_ENV === "production",
        // Refetch on reconnect
        refetchOnReconnect: true,
        // Refetch on mount if data is stale
        refetchOnMount: true,
      },
      mutations: {
        // Retry failed mutations
        retry: 1,
        // Retry delay for mutations
        retryDelay: 1000,
      },
    },
  })
}

/**
 * Global query client instance
 * Use this for server-side rendering or when you need a singleton
 */
export const queryClient = createQueryClient()

/**
 * Query keys factory for consistent key management
 */
export const queryKeys = {
  all: ["queries"] as const,
  users: () => [...queryKeys.all, "users"] as const,
  user: (id: string) => [...queryKeys.users(), id] as const,
  posts: () => [...queryKeys.all, "posts"] as const,
  post: (id: string) => [...queryKeys.posts(), id] as const,
  // Add more query keys as needed
} as const
