[{"C:\\CODES\\next-qop-test\\src\\app\\layout.tsx": "1", "C:\\CODES\\next-qop-test\\src\\app\\page.tsx": "2", "C:\\CODES\\next-qop-test\\src\\components\\ui\\button.tsx": "3", "C:\\CODES\\next-qop-test\\src\\components\\ui\\card.tsx": "4", "C:\\CODES\\next-qop-test\\src\\components\\ui\\dialog.tsx": "5", "C:\\CODES\\next-qop-test\\src\\components\\ui\\dropdown-menu.tsx": "6", "C:\\CODES\\next-qop-test\\src\\components\\ui\\input.tsx": "7", "C:\\CODES\\next-qop-test\\src\\components\\ui\\label.tsx": "8", "C:\\CODES\\next-qop-test\\src\\components\\ui\\select.tsx": "9", "C:\\CODES\\next-qop-test\\src\\components\\ui\\sonner.tsx": "10", "C:\\CODES\\next-qop-test\\src\\components\\ui\\textarea.tsx": "11", "C:\\CODES\\next-qop-test\\src\\config\\env.ts": "12", "C:\\CODES\\next-qop-test\\src\\lib\\utils.ts": "13", "C:\\CODES\\next-qop-test\\src\\types\\index.ts": "14"}, {"size": 681, "mtime": 1750528759241, "results": "15", "hashOfConfig": "16"}, {"size": 4084, "mtime": 1750528759258, "results": "17", "hashOfConfig": "16"}, {"size": 2123, "mtime": 1750528684137, "results": "18", "hashOfConfig": "16"}, {"size": 1989, "mtime": 1750528684148, "results": "19", "hashOfConfig": "16"}, {"size": 3982, "mtime": 1750528684187, "results": "20", "hashOfConfig": "16"}, {"size": 8284, "mtime": 1750528684180, "results": "21", "hashOfConfig": "16"}, {"size": 967, "mtime": 1750528684150, "results": "22", "hashOfConfig": "16"}, {"size": 611, "mtime": 1750528684152, "results": "23", "hashOfConfig": "16"}, {"size": 6253, "mtime": 1750528684163, "results": "24", "hashOfConfig": "16"}, {"size": 564, "mtime": 1750528684190, "results": "25", "hashOfConfig": "16"}, {"size": 759, "mtime": 1750528684155, "results": "26", "hashOfConfig": "16"}, {"size": 1108, "mtime": 1750528759416, "results": "27", "hashOfConfig": "16"}, {"size": 1292, "mtime": 1750528665139, "results": "28", "hashOfConfig": "16"}, {"size": 1121, "mtime": 1750528481555, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wjf14q", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\CODES\\next-qop-test\\src\\app\\layout.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\app\\page.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\button.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\card.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\dialog.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\input.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\label.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\select.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\sonner.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\components\\ui\\textarea.tsx", [], [], "C:\\CODES\\next-qop-test\\src\\config\\env.ts", ["72"], [], "C:\\CODES\\next-qop-test\\src\\lib\\utils.ts", ["73", "74"], [], "C:\\CODES\\next-qop-test\\src\\types\\index.ts", ["75", "76"], [], {"ruleId": "77", "severity": 1, "message": "78", "line": 26, "column": 5, "nodeType": "79", "messageId": "80", "endLine": 26, "endColumn": 18, "suggestions": "81"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 40, "column": 46, "nodeType": "84", "messageId": "85", "endLine": 40, "endColumn": 49, "suggestions": "86"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 40, "column": 56, "nodeType": "84", "messageId": "85", "endLine": 40, "endColumn": 59, "suggestions": "87"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 14, "column": 34, "nodeType": "84", "messageId": "85", "endLine": 14, "endColumn": 37, "suggestions": "88"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 21, "column": 40, "nodeType": "84", "messageId": "85", "endLine": 21, "endColumn": 43, "suggestions": "89"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["90"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["91", "92"], ["93", "94"], ["95", "96"], ["97", "98"], {"fix": "99", "messageId": "100", "data": "101", "desc": "102"}, {"messageId": "103", "fix": "104", "desc": "105"}, {"messageId": "106", "fix": "107", "desc": "108"}, {"messageId": "103", "fix": "109", "desc": "105"}, {"messageId": "106", "fix": "110", "desc": "108"}, {"messageId": "103", "fix": "111", "desc": "105"}, {"messageId": "106", "fix": "112", "desc": "108"}, {"messageId": "103", "fix": "113", "desc": "105"}, {"messageId": "106", "fix": "114", "desc": "108"}, {"range": "115", "text": "116"}, "removeConsole", {"propertyName": "117"}, "Remove the console.error().", "suggestUnknown", {"range": "118", "text": "119"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "120", "text": "121"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "122", "text": "119"}, {"range": "123", "text": "121"}, {"range": "124", "text": "119"}, {"range": "125", "text": "121"}, {"range": "126", "text": "119"}, {"range": "127", "text": "121"}, [672, 728], "", "error", [899, 902], "unknown", [899, 902], "never", [909, 912], [909, 912], [215, 218], [215, 218], [329, 332], [329, 332]]