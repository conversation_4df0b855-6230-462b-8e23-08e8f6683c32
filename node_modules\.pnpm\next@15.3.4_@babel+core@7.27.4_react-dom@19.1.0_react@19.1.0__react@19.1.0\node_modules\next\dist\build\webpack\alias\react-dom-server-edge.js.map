{"version": 3, "sources": ["../../../../src/build/webpack/alias/react-dom-server-edge.js"], "sourcesContent": ["const ERROR_MESSAGE =\n  'Internal Error: do not use legacy react-dom/server APIs. If you encountered this error, please open an issue on the Next.js repo.'\n\nfunction error() {\n  throw new Error(ERROR_MESSAGE)\n}\n\nvar b\nif (process.env.NODE_ENV === 'production') {\n  b = require('next/dist/compiled/react-dom/cjs/react-dom-server.edge.production.js')\n} else {\n  b = require('next/dist/compiled/react-dom/cjs/react-dom-server.edge.development.js')\n}\n\nexports.version = b.version\nexports.renderToReadableStream = b.renderToReadableStream\nexports.renderToString = error\nexports.renderToStaticMarkup = error\nif (b.resume) {\n  exports.resume = b.resume\n}\n"], "names": ["ERROR_MESSAGE", "error", "Error", "b", "process", "env", "NODE_ENV", "require", "exports", "version", "renderToReadableStream", "renderToString", "renderToStaticMarkup", "resume"], "mappings": ";AAAA,MAAMA,gBACJ;AAEF,SAASC;IACP,MAAM,qBAAwB,CAAxB,IAAIC,MAAMF,gBAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAuB;AAC/B;AAEA,IAAIG;AACJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCH,IAAII,QAAQ;AACd,OAAO;IACLJ,IAAII,QAAQ;AACd;AAEAC,QAAQC,OAAO,GAAGN,EAAEM,OAAO;AAC3BD,QAAQE,sBAAsB,GAAGP,EAAEO,sBAAsB;AACzDF,QAAQG,cAAc,GAAGV;AACzBO,QAAQI,oBAAoB,GAAGX;AAC/B,IAAIE,EAAEU,MAAM,EAAE;IACZL,QAAQK,MAAM,GAAGV,EAAEU,MAAM;AAC3B"}