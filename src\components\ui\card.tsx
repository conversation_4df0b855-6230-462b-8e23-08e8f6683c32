import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"
import { ArrowUpRight, Clock, Info } from "lucide-react"
import * as React from "react"

const cardVariants = cva("relative", {
  variants: {
    variant: {
      default: [
        "bg-card text-card-foreground",
        "border rounded-xl shadow-sm",
        "transition-all duration-200",
        "hover:shadow-md",
      ],
      product: [
        "bg-card text-card-foreground",
        "border rounded-xl shadow-sm",
        "transition-all duration-200",
        "hover:shadow-md overflow-hidden",
      ],
      transaction: [
        "bg-card text-card-foreground",
        "border rounded-xl shadow-sm",
        "transition-all duration-200",
        "hover:shadow-md",
        "border-l-4",
      ],
      dashboard: [
        "bg-card text-card-foreground",
        "border rounded-xl shadow-sm",
        "transition-all duration-200",
        "hover:shadow-md",
        "overflow-hidden",
      ],
    },
    status: {
      default: "",
      success: "border-l-green-500",
      warning: "border-l-yellow-500",
      error: "border-l-red-500",
      info: "border-l-blue-500",
    }
  },
  defaultVariants: {
    variant: "default",
    status: "default",
  },
})

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  badge?: string
  badgeColor?: "default" | "success" | "warning" | "error" | "info"
}

function Card({
  className,
  variant,
  status,
  badge,
  badgeColor = "default",
  ...props
}: CardProps) {
  const badgeColorClasses = {
    default: "bg-muted text-muted-foreground",
    success: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
    warning: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
    error: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
    info: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
  }

  return (
    <div
      data-slot="card"
      className={cn(cardVariants({ variant, status, className }))}
      {...props}
    >
      {badge && (
        <div className={cn(
          "absolute top-3 right-3 px-2 py-1 text-xs font-medium rounded-full",
          badgeColorClasses[badgeColor]
        )}>
          {badge}
        </div>
      )}
      {props.children}
    </div>
  )
}

function CardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-header"
      className={cn(
        "grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 pt-6 has-data-[slot=card-action]:grid-cols-[1fr_auto]",
        className
      )}
      {...props}
    />
  )
}

function CardTitle({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-title"
      className={cn("text-lg font-semibold leading-none tracking-tight", className)}
      {...props}
    />
  )
}

function CardDescription({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-description"
      className={cn("text-sm text-muted-foreground", className)}
      {...props}
    />
  )
}

function CardAction({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-action"
      className={cn(
        "col-start-2 row-span-2 row-start-1 self-start justify-self-end",
        className
      )}
      {...props}
    />
  )
}

function CardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-content"
      className={cn("px-6 py-4", className)}
      {...props}
    />
  )
}

function CardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="card-footer"
      className={cn("flex items-center justify-between px-6 py-4 mt-auto border-t", className)}
      {...props}
    />
  )
}

function CardImage({
  src,
  alt,
  className,
  ...props
}: React.ImgHTMLAttributes<HTMLImageElement>) {
  return (
    <div className="relative w-full overflow-hidden">
      <img
        src={src}
        alt={alt || "Card image"}
        className={cn("w-full h-auto object-cover", className)}
        {...props}
      />
    </div>
  )
}

function CardPrice({
  price,
  currency = "$",
  oldPrice,
  className,
  ...props
}: {
  price: string | number
  currency?: string
  oldPrice?: string | number
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("flex items-end gap-2", className)} {...props}>
      <span className="text-xl font-bold">
        {typeof price === 'number' ? `${currency}${price.toFixed(2)}` : `${currency}${price}`}
      </span>
      {oldPrice && (
        <span className="text-sm text-muted-foreground line-through">
          {typeof oldPrice === 'number' ? `${currency}${oldPrice.toFixed(2)}` : `${currency}${oldPrice}`}
        </span>
      )}
    </div>
  )
}

function CardTag({
  children,
  variant = "default",
  className,
  ...props
}: {
  variant?: "default" | "success" | "warning" | "error" | "info"
} & React.HTMLAttributes<HTMLDivElement>) {
  const variantClasses = {
    default: "bg-muted text-muted-foreground",
    success: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
    warning: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
    error: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
    info: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100",
  }

  return (
    <div
      className={cn(
        "inline-flex items-center px-2 py-1 text-xs font-medium rounded-full",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Example usage
function ProductCard() {
  return (
    <Card variant="product" className="w-full max-w-sm">
      <CardImage
        src="https://images.unsplash.com/photo-1523275335684-37898b6baf30"
        alt="Smart Watch"
        className="h-60"
      />
      <CardContent>
        <div className="flex gap-2 mb-2">
          <CardTag variant="info">New</CardTag>
          <CardTag variant="success">In Stock</CardTag>
        </div>
        <CardTitle>Premium Smart Watch</CardTitle>
        <CardDescription className="mt-2">
          Track your fitness goals, receive notifications, and more with this premium smart watch.
        </CardDescription>
        <CardPrice price={199.99} oldPrice={249.99} className="mt-4" />
      </CardContent>
      <CardFooter>
        <button className="text-sm font-medium text-primary flex items-center gap-1 hover:underline">
          <Info className="h-4 w-4" />
          Details
        </button>
        <button className="bg-primary text-primary-foreground px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-1">
          Add to Cart
          <ArrowUpRight className="h-4 w-4" />
        </button>
      </CardFooter>
    </Card>
  )
}

function TransactionCard() {
  return (
    <Card variant="transaction" status="success" className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Payment Received</CardTitle>
        <CardDescription>Transaction ID: #38429</CardDescription>
        <CardAction>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>Today, 2:34 PM</span>
          </div>
        </CardAction>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-muted-foreground">Amount</p>
            <p className="text-xl font-bold">$1,250.00</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Status</p>
            <p className="text-green-600 font-medium">Completed</p>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <button className="text-sm font-medium text-primary flex items-center gap-1 hover:underline">
          View Details
        </button>
        <button className="text-sm font-medium text-muted-foreground hover:text-foreground">
          Download Receipt
        </button>
      </CardFooter>
    </Card>
  )
}

function DashboardCard() {
  return (
    <Card variant="dashboard" className="w-full max-w-sm">
      <CardHeader className="bg-primary/5 border-b">
        <CardTitle>Monthly Revenue</CardTitle>
        <CardDescription>Overview of your store's performance</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Current Month</span>
            <span className="text-xl font-bold">$24,568.80</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground">Previous Month</span>
            <span className="text-sm">$21,345.50</span>
          </div>
          <div className="flex justify-between items-center mt-2">
            <span className="text-sm text-muted-foreground">Growth</span>
            <span className="text-sm text-green-600 font-medium">+15.1%</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <button className="text-sm font-medium text-primary flex items-center gap-1 hover:underline">
          View Full Report
          <ArrowUpRight className="h-4 w-4" />
        </button>
      </CardFooter>
    </Card>
  )
}

export {
  Card, CardAction, CardContent, CardDescription, CardFooter, CardHeader, CardImage,
  CardPrice,
  CardTag, CardTitle, DashboardCard, ProductCard,
  TransactionCard
}

// Example usage
export default function CardExample() {
  return (
    <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <ProductCard />
      <TransactionCard />
      <DashboardCard />
    </div>
  )
}
