{"name": "@typescript-eslint/parser", "version": "8.34.1", "description": "An ESLint custom parser which leverages TypeScript ESTree", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/parser"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io/packages/parser", "license": "MIT", "keywords": ["ast", "ecmascript", "javascript", "typescript", "parser", "syntax", "eslint"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}, "dependencies": {"@typescript-eslint/scope-manager": "8.34.1", "@typescript-eslint/types": "8.34.1", "@typescript-eslint/typescript-estree": "8.34.1", "@typescript-eslint/visitor-keys": "8.34.1", "debug": "^4.3.4"}, "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "glob": "*", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "nx": {"name": "parser", "includedScripts": ["clean"]}}