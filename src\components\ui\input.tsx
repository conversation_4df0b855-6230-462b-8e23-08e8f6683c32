"use client"

import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import { <PERSON>ertCircle, CheckCircle2, Eye, EyeOff } from "lucide-react"
import * as React from "react"

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  helperText?: string
  errorMessage?: string
  successMessage?: string
  isError?: boolean
  isSuccess?: boolean
  showPasswordToggle?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  formatAs?: "creditCard" | "phone" | "currency" | "date" | "numeric"
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    type = "text",
    label,
    helperText,
    errorMessage,
    successMessage,
    isError,
    isSuccess,
    showPasswordToggle,
    leftIcon,
    rightIcon,
    formatAs,
    id,
    disabled,
    required,
    ...props
  }, ref) => {
    const [inputType, setInputType] = React.useState(type)
    const [isFocused, setIsFocused] = React.useState(false)
    const [hasValue, setHasValue] = React.useState(!!props.value || !!props.defaultValue)
    const inputId = id || React.useId()

    // Handle password visibility toggle
    const togglePasswordVisibility = () => {
      setInputType(inputType === "password" ? "text" : "password")
    }

    // Format input based on type
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value

      if (formatAs === "creditCard" && value) {
        // Format as credit card: 1234 5678 9012 3456
        value = value.replace(/\s/g, "").replace(/(.{4})/g, "$1 ").trim()
        e.target.value = value.substring(0, 19)
      } else if (formatAs === "phone" && value) {
        // Format as phone: (*************
        value = value.replace(/\D/g, "")
        const match = value.match(/^(\d{0,3})(\d{0,3})(\d{0,4})$/)
        if (match) {
          value = !match[2] ? match[1] : `(${match[1]}) ${match[2]}${match[3] ? `-${match[3]}` : ""}`
        }
        e.target.value = value
      } else if (formatAs === "currency" && value) {
        // Format as currency: $1,234.56
        value = value.replace(/[^\d.]/g, "")
        const parts = value.split(".")
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",")
        e.target.value = parts.length > 1 ? `${parts[0]}.${parts[1].substring(0, 2)}` : parts[0]
      } else if (formatAs === "date" && value) {
        // Format as date: MM/DD/YYYY
        value = value.replace(/\D/g, "")
        const match = value.match(/^(\d{0,2})(\d{0,2})(\d{0,4})$/)
        if (match) {
          value = !match[2] ? match[1] : `${match[1]}/${match[2]}${match[3] ? `/${match[3]}` : ""}`
        }
        e.target.value = value
      } else if (formatAs === "numeric" && value) {
        // Only allow numbers
        e.target.value = value.replace(/[^\d]/g, "")
      }

      setHasValue(!!e.target.value)
      props.onChange?.(e)
    }

    // Determine if we should show the floating label effect
    const shouldFloat = isFocused || hasValue

    return (
      <div className="relative w-full">
        {label && (
          <Label
            htmlFor={inputId}
            className={cn(
              "absolute left-3 transition-all duration-200 pointer-events-none",
              shouldFloat
                ? "-top-2 text-xs bg-background px-1 z-10"
                : "top-1/2 -translate-y-1/2 text-sm text-muted-foreground",
              (isError || isSuccess) && shouldFloat && "left-6",
              disabled && "opacity-50"
            )}
          >
            {label}{required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}

        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}

          {(isError || isSuccess) && (
            <div className={cn(
              "absolute left-3 top-1/2 -translate-y-1/2",
              isError ? "text-destructive" : "text-success"
            )}>
              {isError ? <AlertCircle size={16} /> : <CheckCircle2 size={16} />}
            </div>
          )}

          <input
            id={inputId}
            type={inputType}
            data-slot="input"
            ref={ref}
            disabled={disabled}
            required={required}
            className={cn(
              "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-10 w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-all outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
              "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
              isError && "border-destructive focus-visible:ring-destructive/50",
              isSuccess && "border-success focus-visible:ring-success/50",
              leftIcon && "pl-10",
              (rightIcon || showPasswordToggle) && "pr-10",
              (isError || isSuccess) && "pl-10",
              className
            )}
            onFocus={(e) => {
              setIsFocused(true)
              props.onFocus?.(e)
            }}
            onBlur={(e) => {
              setIsFocused(false)
              props.onBlur?.(e)
            }}
            onChange={handleChange}
            {...props}
          />

          {showPasswordToggle && type === "password" && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground focus:outline-none"
              tabIndex={-1}
              aria-label={inputType === "password" ? "Show password" : "Hide password"}
            >
              {inputType === "password" ? <Eye size={16} /> : <EyeOff size={16} />}
            </button>
          )}

          {rightIcon && !showPasswordToggle && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {rightIcon}
            </div>
          )}
        </div>

        {(helperText || errorMessage || successMessage) && (
          <div className={cn(
            "text-xs mt-1 transition-all",
            isError ? "text-destructive" : isSuccess ? "text-success" : "text-muted-foreground"
          )}>
            {isError ? errorMessage : isSuccess ? successMessage : helperText}
          </div>
        )}
      </div>
    )
  }
)

Input.displayName = "Input"

export { Input }

// Example usage
const InputExample = () => {
  const [value, setValue] = React.useState("")
  const [error, setError] = React.useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)
    setError(newValue.length < 3 && newValue.length > 0)
  }

  return (
    <div className="space-y-6 w-full max-w-sm mx-auto">
      <Input
        label="Email Address"
        type="email"
        placeholder="<EMAIL>"
        helperText="We'll never share your email with anyone else."
      />

      <Input
        label="Password"
        type="password"
        placeholder="Enter your password"
        showPasswordToggle
        helperText="Password must be at least 8 characters"
      />

      <Input
        label="Credit Card"
        placeholder="1234 5678 9012 3456"
        formatAs="creditCard"
        helperText="We use secure encryption for all transactions"
      />

      <Input
        label="Amount"
        placeholder="0.00"
        formatAs="currency"
        leftIcon={<span className="text-xs font-medium">$</span>}
      />

      <Input
        label="Username"
        value={value}
        onChange={handleChange}
        isError={error}
        errorMessage="Username must be at least 3 characters"
        required
      />
    </div>
  )
}

export default InputExample
