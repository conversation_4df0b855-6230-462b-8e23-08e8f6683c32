"use client"

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, CheckCircle, <PERSON>, Palette, Zap } from "lucide-react"

import { MainLayout } from "@/components/layout/main-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Home() {
  const features = [
    {
      icon: <Zap className="h-6 w-6" />,
      title: "Next.js 15",
      description: "Built with the latest Next.js with App Router and React Server Components",
    },
    {
      icon: <Palette className="h-6 w-6" />,
      title: "shadcn/ui",
      description: "Beautiful and accessible components built with Radix UI and Tailwind CSS",
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: "TypeScript",
      description: "Fully typed with TypeScript for better developer experience",
    },
  ]

  const setupItems = [
    "Next.js 15 with TypeScript and App Router",
    "shadcn/ui component library with Tailwind CSS",
    "ESLint and Prettier configuration",
    "React Query for state management",
    "Axios HTTP client with interceptors",
    "Environment configuration with validation",
    "Dark/light mode toggle with next-themes",
    "API routes with proper error handling",
    "Production-ready folder structure",
  ]

  return (
    <MainLayout>
      <div className="container mx-auto">
        {/* Hero Section */}
        <section className="py-20 text-center">
          <div className="mx-auto max-w-4xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
              Next.js Production-Ready
              <span className="text-primary"> Starter</span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              A comprehensive Next.js 15 starter with TypeScript, shadcn/ui, React Query,
              and all the tools you need for production-grade applications.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button asChild size="lg">
                <Link href="/components">
                  View Components
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="https://github.com" target="_blank">
                  View on GitHub
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight text-center mb-12">
              Built with Modern Tools
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <Card key={index}>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <div className="text-primary">{feature.icon}</div>
                      <CardTitle className="text-xl">{feature.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* What's Included Section */}
        <section className="py-20">
          <div className="mx-auto max-w-4xl">
            <h2 className="text-3xl font-bold tracking-tight text-center mb-12">
              What's Included
            </h2>
            <Card>
              <CardHeader>
                <CardTitle>Complete Development Setup</CardTitle>
                <CardDescription>
                  Everything you need to start building production-ready applications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {setupItems.map((item, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{item}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 text-center">
          <div className="mx-auto max-w-2xl">
            <h2 className="text-3xl font-bold tracking-tight mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Explore the components and start building your next project.
            </p>
            <div className="flex items-center justify-center gap-4">
              <Button asChild size="lg">
                <Link href="/components">
                  Explore Components
                </Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/api/health" target="_blank">
                  Test API
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </div>
    </MainLayout>
  )
}
