declare const _default: {
    extends: string[];
    rules: {
        '@typescript-eslint/adjacent-overload-signatures': "error";
        '@typescript-eslint/array-type': "error";
        '@typescript-eslint/ban-tslint-comment': "error";
        '@typescript-eslint/class-literal-property-style': "error";
        '@typescript-eslint/consistent-generic-constructors': "error";
        '@typescript-eslint/consistent-indexed-object-style': "error";
        '@typescript-eslint/consistent-type-assertions': "error";
        '@typescript-eslint/consistent-type-definitions': "error";
        'dot-notation': "off";
        '@typescript-eslint/dot-notation': "error";
        '@typescript-eslint/no-confusing-non-null-assertion': "error";
        'no-empty-function': "off";
        '@typescript-eslint/no-empty-function': "error";
        '@typescript-eslint/no-inferrable-types': "error";
        '@typescript-eslint/non-nullable-type-assertion-style': "error";
        '@typescript-eslint/prefer-find': "error";
        '@typescript-eslint/prefer-for-of': "error";
        '@typescript-eslint/prefer-function-type': "error";
        '@typescript-eslint/prefer-includes': "error";
        '@typescript-eslint/prefer-nullish-coalescing': "error";
        '@typescript-eslint/prefer-optional-chain': "error";
        '@typescript-eslint/prefer-regexp-exec': "error";
        '@typescript-eslint/prefer-string-starts-ends-with': "error";
    };
};
export = _default;
//# sourceMappingURL=stylistic-type-checked.d.ts.map