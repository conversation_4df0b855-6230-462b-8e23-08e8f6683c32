{"version": 3, "sources": ["../../../../../../src/build/webpack/loaders/resolve-url-loader/lib/value-processor.ts"], "sourcesContent": ["/*\nThe MIT License (MIT)\n\nCopyright (c) 2016 Ben <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport loaderUtils from 'next/dist/compiled/loader-utils2'\nimport path from 'path'\n\nfunction valueProcessor(filename: any, options: any) {\n  const URL_STATEMENT_REGEX =\n    /(url\\s*\\()\\s*(?:(['\"])((?:(?!\\2).)*)(\\2)|([^'\"](?:(?!\\)).)*[^'\"]))\\s*(\\))/g\n  const directory = path.dirname(filename)\n  const join = options.join(filename, options)\n\n  /**\n   * Process the given CSS declaration value.\n   *\n   */\n  return function transformValue(\n    /** A declaration value that may or may not contain a url() statement */\n    value: string,\n    /** An absolute path that may be the correct base or an Iterator thereof */\n    candidate: any\n  ) {\n    // allow multiple url() values in the declaration\n    //  split by url statements and process the content\n    //  additional capture groups are needed to match quotations correctly\n    //  escaped quotations are not considered\n    return value\n      .split(URL_STATEMENT_REGEX)\n      .map((token: any, i: any, arr: any) => {\n        // we can get groups as undefined under certain match circumstances\n        const initialised = token || ''\n\n        // the content of the url() statement is either in group 3 or group 5\n        const mod = i % 7\n        if (mod === 3 || mod === 5) {\n          // detect quoted url and unescape backslashes\n          const before = arr[i - 1],\n            after = arr[i + 1],\n            isQuoted = before === after && (before === \"'\" || before === '\"'),\n            unescaped = isQuoted\n              ? initialised.replace(/\\\\{2}/g, '\\\\')\n              : initialised\n\n          // split into uri and query/hash and then find the absolute path to the uri\n          const split = unescaped.split(/([?#])/g),\n            uri = split[0],\n            absolute =\n              // eslint-disable-next-line @typescript-eslint/no-use-before-define\n              (testIsRelative(uri) && join(uri, candidate)) ||\n              // eslint-disable-next-line @typescript-eslint/no-use-before-define\n              (testIsAbsolute(uri) && join(uri)),\n            query = options.keepQuery ? split.slice(1).join('') : ''\n\n          // use the absolute path in absolute mode or else relative path (or default to initialised)\n          // #6 - backslashes are not legal in URI\n          if (!absolute) {\n            return initialised\n          } else if (options.absolute) {\n            return absolute.replace(/\\\\/g, '/') + query\n          } else {\n            return loaderUtils.urlToRequest(\n              path.relative(directory, absolute).replace(/\\\\/g, '/') + query\n            )\n          }\n        }\n        // everything else, including parentheses and quotation (where present) and media statements\n        else {\n          return initialised\n        }\n      })\n      .join('')\n  }\n\n  /**\n   * The loaderUtils.isUrlRequest() doesn't support windows absolute paths on principle. We do not subscribe to that\n   * dogma so we add path.isAbsolute() check to allow them.\n   *\n   * We also eliminate module relative (~) paths.\n   *\n   * Returns true for relative uri\n   */\n  function testIsRelative(\n    /** A uri string possibly empty or undefined */\n    uri?: string\n  ): boolean {\n    return (\n      !!uri &&\n      loaderUtils.isUrlRequest(uri, false) &&\n      !path.isAbsolute(uri) &&\n      uri.indexOf('~') !== 0\n    )\n  }\n\n  /**\n   * The loaderUtils.isUrlRequest() doesn't support windows absolute paths on principle. We do not subscribe to that\n   * dogma so we add path.isAbsolute() check to allow them.\n   *\n   * Returns true for absolute uri\n   */\n  function testIsAbsolute(\n    /** A uri string possibly empty or undefined */\n    uri?: string\n  ) {\n    return (\n      !!uri &&\n      typeof options.root === 'string' &&\n      loaderUtils.isUrlRequest(uri, options.root) &&\n      (/^\\//.test(uri) || path.isAbsolute(uri))\n    )\n  }\n}\n\nexport default valueProcessor\n"], "names": ["valueProcessor", "filename", "options", "URL_STATEMENT_REGEX", "directory", "path", "dirname", "join", "transformValue", "value", "candidate", "split", "map", "token", "i", "arr", "initialised", "mod", "before", "after", "isQuoted", "unescaped", "replace", "uri", "absolute", "testIsRelative", "testIsAbsolute", "query", "<PERSON><PERSON><PERSON><PERSON>", "slice", "loaderUtils", "urlToRequest", "relative", "isUrlRequest", "isAbsolute", "indexOf", "root", "test"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;+BA+GA;;;eAAA;;;qEA7GwB;6DACP;;;;;;AAEjB,SAASA,eAAeC,QAAa,EAAEC,OAAY;IACjD,MAAMC,sBACJ;IACF,MAAMC,YAAYC,aAAI,CAACC,OAAO,CAACL;IAC/B,MAAMM,OAAOL,QAAQK,IAAI,CAACN,UAAUC;IAEpC;;;GAGC,GACD,OAAO,SAASM,eACd,sEAAsE,GACtEC,KAAa,EACb,yEAAyE,GACzEC,SAAc;QAEd,iDAAiD;QACjD,mDAAmD;QACnD,sEAAsE;QACtE,yCAAyC;QACzC,OAAOD,MACJE,KAAK,CAACR,qBACNS,GAAG,CAAC,CAACC,OAAYC,GAAQC;YACxB,mEAAmE;YACnE,MAAMC,cAAcH,SAAS;YAE7B,qEAAqE;YACrE,MAAMI,MAAMH,IAAI;YAChB,IAAIG,QAAQ,KAAKA,QAAQ,GAAG;gBAC1B,6CAA6C;gBAC7C,MAAMC,SAASH,GAAG,CAACD,IAAI,EAAE,EACvBK,QAAQJ,GAAG,CAACD,IAAI,EAAE,EAClBM,WAAWF,WAAWC,SAAUD,CAAAA,WAAW,OAAOA,WAAW,GAAE,GAC/DG,YAAYD,WACRJ,YAAYM,OAAO,CAAC,UAAU,QAC9BN;gBAEN,2EAA2E;gBAC3E,MAAML,QAAQU,UAAUV,KAAK,CAAC,YAC5BY,MAAMZ,KAAK,CAAC,EAAE,EACda,WAEE,AADA,mEAAmE;gBAClEC,eAAeF,QAAQhB,KAAKgB,KAAKb,cAClC,mEAAmE;gBAClEgB,eAAeH,QAAQhB,KAAKgB,MAC/BI,QAAQzB,QAAQ0B,SAAS,GAAGjB,MAAMkB,KAAK,CAAC,GAAGtB,IAAI,CAAC,MAAM;gBAExD,2FAA2F;gBAC3F,wCAAwC;gBACxC,IAAI,CAACiB,UAAU;oBACb,OAAOR;gBACT,OAAO,IAAId,QAAQsB,QAAQ,EAAE;oBAC3B,OAAOA,SAASF,OAAO,CAAC,OAAO,OAAOK;gBACxC,OAAO;oBACL,OAAOG,qBAAW,CAACC,YAAY,CAC7B1B,aAAI,CAAC2B,QAAQ,CAAC5B,WAAWoB,UAAUF,OAAO,CAAC,OAAO,OAAOK;gBAE7D;YACF,OAEK;gBACH,OAAOX;YACT;QACF,GACCT,IAAI,CAAC;IACV;IAEA;;;;;;;GAOC,GACD,SAASkB,eACP,6CAA6C,GAC7CF,GAAY;QAEZ,OACE,CAAC,CAACA,OACFO,qBAAW,CAACG,YAAY,CAACV,KAAK,UAC9B,CAAClB,aAAI,CAAC6B,UAAU,CAACX,QACjBA,IAAIY,OAAO,CAAC,SAAS;IAEzB;IAEA;;;;;GAKC,GACD,SAAST,eACP,6CAA6C,GAC7CH,GAAY;QAEZ,OACE,CAAC,CAACA,OACF,OAAOrB,QAAQkC,IAAI,KAAK,YACxBN,qBAAW,CAACG,YAAY,CAACV,KAAKrB,QAAQkC,IAAI,KACzC,CAAA,MAAMC,IAAI,CAACd,QAAQlB,aAAI,CAAC6B,UAAU,CAACX,IAAG;IAE3C;AACF;MAEA,WAAevB"}