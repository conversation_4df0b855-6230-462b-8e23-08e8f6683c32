import { render, screen } from '@/test-utils'

import Home from '@/app/page'

// Mock the MainLayout component
jest.mock('@/components/layout/main-layout', () => {
  return {
    MainLayout: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="main-layout">{children}</div>
    ),
  }
})

describe('Home Page', () => {
  it('renders the hero section', () => {
    render(<Home />)
    
    expect(
      screen.getByRole('heading', { name: /next\.js production-ready starter/i })
    ).toBeInTheDocument()
    
    expect(
      screen.getByText(/comprehensive next\.js 15 starter/i)
    ).toBeInTheDocument()
  })

  it('renders the features section', () => {
    render(<Home />)

    expect(
      screen.getByRole('heading', { name: /built with modern tools/i })
    ).toBeInTheDocument()

    expect(screen.getAllByText(/next\.js 15/i)).toHaveLength(3)
    expect(screen.getAllByText(/shadcn\/ui/i)).toHaveLength(3)
    expect(screen.getAllByText(/typescript/i)).toHaveLength(4)
  })

  it('renders the what\'s included section', () => {
    render(<Home />)
    
    expect(
      screen.getByRole('heading', { name: /what's included/i })
    ).toBeInTheDocument()
    
    expect(
      screen.getByText(/next\.js 15 with typescript and app router/i)
    ).toBeInTheDocument()
    expect(
      screen.getByText(/shadcn\/ui component library/i)
    ).toBeInTheDocument()
  })

  it('renders the CTA section', () => {
    render(<Home />)
    
    expect(
      screen.getByRole('heading', { name: /ready to get started/i })
    ).toBeInTheDocument()
    
    expect(
      screen.getByRole('link', { name: /explore components/i })
    ).toBeInTheDocument()
    expect(
      screen.getByRole('link', { name: /test api/i })
    ).toBeInTheDocument()
  })

  it('has correct navigation links', () => {
    render(<Home />)
    
    const componentsLink = screen.getByRole('link', { name: /view components/i })
    expect(componentsLink).toHaveAttribute('href', '/components')
    
    const githubLink = screen.getByRole('link', { name: /view on github/i })
    expect(githubLink).toHaveAttribute('href', 'https://github.com')
    expect(githubLink).toHaveAttribute('target', '_blank')
    
    const exploreLink = screen.getByRole('link', { name: /explore components/i })
    expect(exploreLink).toHaveAttribute('href', '/components')
    
    const apiLink = screen.getByRole('link', { name: /test api/i })
    expect(apiLink).toHaveAttribute('href', '/api/health')
    expect(apiLink).toHaveAttribute('target', '_blank')
  })

  it('renders all setup items', () => {
    render(<Home />)
    
    const setupItems = [
      'Next.js 15 with TypeScript and App Router',
      'shadcn/ui component library with Tailwind CSS',
      'ESLint and Prettier configuration',
      'React Query for state management',
      'Axios HTTP client with interceptors',
      'Environment configuration with validation',
      'Dark/light mode toggle with next-themes',
      'API routes with proper error handling',
      'Production-ready folder structure',
    ]
    
    setupItems.forEach(item => {
      expect(screen.getByText(item)).toBeInTheDocument()
    })
  })
})
