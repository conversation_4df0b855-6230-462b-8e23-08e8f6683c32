{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-edge-app-route-loader/index.ts"], "sourcesContent": ["import { getModuleBuildInfo } from '../get-module-build-info'\nimport { stringifyRequest } from '../../stringify-request'\nimport type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { WEBPACK_RESOURCE_QUERIES } from '../../../../lib/constants'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport { isMetadataRoute } from '../../../../lib/metadata/is-metadata-route'\n\nexport type EdgeAppRouteLoaderQuery = {\n  absolutePagePath: string\n  page: string\n  appDirLoader: string\n  preferredRegion: string | string[] | undefined\n  nextConfig: string\n  middlewareConfig: string\n  cacheHandlers: string\n}\n\nconst EdgeAppRouteLoader: webpack.LoaderDefinitionFunction<EdgeAppRouteLoaderQuery> =\n  async function (this) {\n    const {\n      page,\n      absolutePagePath,\n      preferredRegion,\n      appDirLoader: appDirLoaderBase64 = '',\n      middlewareConfig: middlewareConfigBase64 = '',\n      nextConfig: nextConfigBase64,\n      cacheHandlers: cacheHandlersStringified,\n    } = this.getOptions()\n\n    const appDirLoader = Buffer.from(appDirLoaderBase64, 'base64').toString()\n    const middlewareConfig: MiddlewareConfig = JSON.parse(\n      Buffer.from(middlewareConfigBase64, 'base64').toString()\n    )\n\n    const cacheHandlers = JSON.parse(cacheHandlersStringified || '{}')\n\n    if (!cacheHandlers.default) {\n      cacheHandlers.default = require.resolve(\n        '../../../../server/lib/cache-handlers/default'\n      )\n    }\n\n    // Ensure we only run this loader for as a module.\n    if (!this._module) throw new Error('This loader is only usable as a module')\n\n    const buildInfo = getModuleBuildInfo(this._module)\n\n    buildInfo.nextEdgeSSR = {\n      isServerComponent: !isMetadataRoute(page), // Needed for 'use cache'.\n      page: page,\n      isAppDir: true,\n    }\n    buildInfo.route = {\n      page,\n      absolutePagePath,\n      preferredRegion,\n      middlewareConfig,\n    }\n\n    const stringifiedPagePath = stringifyRequest(this, absolutePagePath)\n    const modulePath = `${appDirLoader}${stringifiedPagePath.substring(\n      1,\n      stringifiedPagePath.length - 1\n    )}?${WEBPACK_RESOURCE_QUERIES.edgeSSREntry}`\n\n    const stringifiedConfig = Buffer.from(\n      nextConfigBase64 || '',\n      'base64'\n    ).toString()\n\n    return await loadEntrypoint(\n      'edge-app-route',\n      {\n        VAR_USERLAND: modulePath,\n        VAR_PAGE: page,\n      },\n      {\n        nextConfig: stringifiedConfig,\n      }\n    )\n  }\n\nexport default EdgeAppRouteLoader\n"], "names": ["EdgeAppRouteLoader", "page", "absolutePagePath", "preferredRegion", "appDirLoader", "appDirLoaderBase64", "middlewareConfig", "middlewareConfigBase64", "nextConfig", "nextConfigBase64", "cacheHandlers", "cacheHandlersStringified", "getOptions", "<PERSON><PERSON><PERSON>", "from", "toString", "JSON", "parse", "default", "require", "resolve", "_module", "Error", "buildInfo", "getModuleBuildInfo", "nextEdgeSSR", "isServerComponent", "isMetadataRoute", "isAppDir", "route", "stringifiedPagePath", "stringifyRequest", "modulePath", "substring", "length", "WEBPACK_RESOURCE_QUERIES", "edgeSSREntry", "stringifiedConfig", "loadEntrypoint", "VAR_USERLAND", "VAR_PAGE"], "mappings": ";;;;+BAmFA;;;eAAA;;;oCAnFmC;kCACF;2BAEQ;gCAEV;iCACC;AAYhC,MAAMA,qBACJ;IACE,MAAM,EACJC,IAAI,EACJC,gBAAgB,EAChBC,eAAe,EACfC,cAAcC,qBAAqB,EAAE,EACrCC,kBAAkBC,yBAAyB,EAAE,EAC7CC,YAAYC,gBAAgB,EAC5BC,eAAeC,wBAAwB,EACxC,GAAG,IAAI,CAACC,UAAU;IAEnB,MAAMR,eAAeS,OAAOC,IAAI,CAACT,oBAAoB,UAAUU,QAAQ;IACvE,MAAMT,mBAAqCU,KAAKC,KAAK,CACnDJ,OAAOC,IAAI,CAACP,wBAAwB,UAAUQ,QAAQ;IAGxD,MAAML,gBAAgBM,KAAKC,KAAK,CAACN,4BAA4B;IAE7D,IAAI,CAACD,cAAcQ,OAAO,EAAE;QAC1BR,cAAcQ,OAAO,GAAGC,QAAQC,OAAO,CACrC;IAEJ;IAEA,kDAAkD;IAClD,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE,MAAM,qBAAmD,CAAnD,IAAIC,MAAM,2CAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAkD;IAE3E,MAAMC,YAAYC,IAAAA,sCAAkB,EAAC,IAAI,CAACH,OAAO;IAEjDE,UAAUE,WAAW,GAAG;QACtBC,mBAAmB,CAACC,IAAAA,gCAAe,EAAC1B;QACpCA,MAAMA;QACN2B,UAAU;IACZ;IACAL,UAAUM,KAAK,GAAG;QAChB5B;QACAC;QACAC;QACAG;IACF;IAEA,MAAMwB,sBAAsBC,IAAAA,kCAAgB,EAAC,IAAI,EAAE7B;IACnD,MAAM8B,aAAa,GAAG5B,eAAe0B,oBAAoBG,SAAS,CAChE,GACAH,oBAAoBI,MAAM,GAAG,GAC7B,CAAC,EAAEC,mCAAwB,CAACC,YAAY,EAAE;IAE5C,MAAMC,oBAAoBxB,OAAOC,IAAI,CACnCL,oBAAoB,IACpB,UACAM,QAAQ;IAEV,OAAO,MAAMuB,IAAAA,8BAAc,EACzB,kBACA;QACEC,cAAcP;QACdQ,UAAUvC;IACZ,GACA;QACEO,YAAY6B;IACd;AAEJ;MAEF,WAAerC"}