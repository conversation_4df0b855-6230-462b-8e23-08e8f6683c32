import { NextRequest } from "next/server"

import { createSuccessResponse, validate<PERSON>ethod } from "@/lib/api-response"
import { withErrorHandling } from "@/lib/api-middleware"

/**
 * GET /api/health - Health check endpoint
 */
async function healthCheck(request: NextRequest) {
  validateMethod(request, ["GET"])

  const healthData = {
    status: "healthy",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: "1.0.0",
    checks: {
      database: "healthy", // Replace with actual database check
      redis: "healthy", // Replace with actual Redis check
      external_api: "healthy", // Replace with actual external API check
    },
  }

  return createSuccessResponse(healthData, "Service is healthy")
}

const handler = withErrorHandling(healthCheck)

export { handler as GET }
