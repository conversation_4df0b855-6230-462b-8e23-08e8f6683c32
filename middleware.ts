import { NextRequest, NextResponse } from "next/server"

/**
 * Global middleware for the application
 * Runs on all requests before they reach the API routes or pages
 */
export function middleware(request: NextRequest) {
  // Add security headers
  const response = NextResponse.next()

  // Security headers
  response.headers.set("X-Frame-Options", "DENY")
  response.headers.set("X-Content-Type-Options", "nosniff")
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin")
  response.headers.set(
    "Permissions-Policy",
    "camera=(), microphone=(), geolocation=()"
  )

  // API-specific middleware
  if (request.nextUrl.pathname.startsWith("/api/")) {
    // Add API-specific headers
    response.headers.set("X-API-Version", "1.0.0")
    
    // Log API requests in development
    if (process.env.NODE_ENV === "development") {
      console.log(`API Request: ${request.method} ${request.nextUrl.pathname}`)
    }
  }

  return response
}

/**
 * Configure which paths the middleware should run on
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
}
