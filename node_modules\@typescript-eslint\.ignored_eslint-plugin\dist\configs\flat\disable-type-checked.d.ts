import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * A utility ruleset that will disable type-aware linting and all type-aware rules available in our project.
 * @see {@link https://typescript-eslint.io/users/configs#disable-type-checked}
 */
declare const _default: (_plugin: FlatConfig.Plugin, _parser: FlatConfig.Parser) => FlatConfig.Config;
export default _default;
//# sourceMappingURL=disable-type-checked.d.ts.map