"use client"

import { useTheme as useNextTheme } from "next-themes"
import { useEffect, useState } from "react"

/**
 * Enhanced theme hook with additional utilities
 * Wraps next-themes useTheme with extra functionality
 */
export function useTheme() {
  const { theme, setTheme, resolvedTheme, themes, systemTheme } = useNextTheme()
  const [mounted, setMounted] = useState(false)

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Helper functions
  const isDark = resolvedTheme === "dark"
  const isLight = resolvedTheme === "light"
  const isSystem = theme === "system"

  const toggleTheme = () => {
    setTheme(isDark ? "light" : "dark")
  }

  const setLightTheme = () => setTheme("light")
  const setDarkTheme = () => setTheme("dark")
  const setSystemTheme = () => setTheme("system")

  return {
    // Original next-themes values
    theme,
    setTheme,
    resolvedTheme,
    themes,
    systemTheme,
    
    // Additional utilities
    mounted,
    isDark,
    isLight,
    isSystem,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme,
  }
}

/**
 * Hook to get theme-aware CSS classes
 */
export function useThemeClasses() {
  const { isDark, isLight, mounted } = useTheme()

  if (!mounted) {
    return {
      isDark: false,
      isLight: false,
      themeClass: "",
    }
  }

  return {
    isDark,
    isLight,
    themeClass: isDark ? "dark" : "light",
  }
}

/**
 * Hook for theme-aware values
 * Returns different values based on the current theme
 */
export function useThemeValue<T>(lightValue: T, darkValue: T): T {
  const { isDark, mounted } = useTheme()

  if (!mounted) {
    return lightValue
  }

  return isDark ? darkValue : lightValue
}
