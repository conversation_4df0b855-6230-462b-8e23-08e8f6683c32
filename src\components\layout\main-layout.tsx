import { Footer } from "./footer"
import { Header } from "./header"

interface MainLayoutProps {
  children: React.ReactNode
}

/**
 * Main layout component that wraps pages with header and footer
 */
export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="relative flex min-h-screen flex-col bg-background">
      <Header />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  )
}
