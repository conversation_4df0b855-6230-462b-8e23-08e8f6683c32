{"version": 3, "sources": ["../../../src/build/jest/object-proxy.ts"], "sourcesContent": ["/*\nThe MIT License (MIT)\n\nCopyright (c) 2015 Key<PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\n// This file is largely based on https://github.com/keyz/identity-obj-proxy\n// Excludes the polyfill for below Node.js 6\nexport default new Proxy(\n  {},\n  {\n    get: function getter(_target, key) {\n      if (key === '__esModule') {\n        return false\n      }\n      return key\n    },\n  }\n)\n"], "names": ["Proxy", "get", "getter", "_target", "key"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GAEA,2EAA2E;AAC3E,4CAA4C;;;;;+BAC5C;;;eAAA;;;MAAA,WAAe,IAAIA,MACjB,CAAC,GACD;IACEC,KAAK,SAASC,OAAOC,OAAO,EAAEC,GAAG;QAC/B,IAAIA,QAAQ,cAAc;YACxB,OAAO;QACT;QACA,OAAOA;IACT;AACF"}