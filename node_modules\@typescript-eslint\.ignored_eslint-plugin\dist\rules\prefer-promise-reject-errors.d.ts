export type MessageIds = 'rejectAnError';
export type Options = [
    {
        allowEmptyReject?: boolean;
        allowThrowingAny?: boolean;
        allowThrowingUnknown?: boolean;
    }
];
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"rejectAnError", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=prefer-promise-reject-errors.d.ts.map